# Google Analytics 快速设置指南

## 🎯 完成设置只需3步

### 第1步：获取Google Analytics ID

1. **访问Google Analytics**
   - 打开 [https://analytics.google.com/](https://analytics.google.com/)
   - 使用您的Google账号登录

2. **创建新属性**
   - 点击"管理"（齿轮图标）
   - 选择"创建属性"
   - 选择"GA4"（Google Analytics 4）
   - 填写网站信息：
     - 属性名称：`Web3项目深度分析报告`
     - 时区：选择您的时区
     - 货币：选择相应货币

3. **获取测量ID**
   - 创建完成后，进入"数据流"
   - 点击"添加流" > "网站"
   - 输入您的网站URL
   - 复制生成的测量ID（格式：`G-XXXXXXXXXX`）

### 第2步：配置环境变量

1. **编辑.env文件**
   ```bash
   # 如果没有.env文件，先复制示例文件
   cp .env.example .env
   ```

2. **添加Google Analytics ID**
   在`.env`文件中添加或修改：
   ```env
   GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
   ```
   将`G-XXXXXXXXXX`替换为您在第1步获取的实际测量ID。

### 第3步：重启应用并验证

1. **重启应用**
   ```bash
   # 如果使用开发服务器
   python run.py
   
   # 如果使用Docker
   docker-compose restart
   
   # 如果部署在Vercel
   # 在Vercel控制台中添加环境变量并重新部署
   ```

2. **验证集成**
   - 访问您的网站
   - 打开浏览器开发者工具（F12）
   - 在Console中查看是否有GA相关的网络请求
   - 访问Google Analytics实时报告，应该能看到您的访问

## 🔧 Vercel部署配置

如果您的网站部署在Vercel上：

1. **在Vercel控制台添加环境变量**
   - 登录 [Vercel控制台](https://vercel.com/dashboard)
   - 选择您的项目
   - 进入"Settings" > "Environment Variables"
   - 添加新变量：
     - Name: `GOOGLE_ANALYTICS_ID`
     - Value: `G-XXXXXXXXXX`
   - 选择适用环境（Production, Preview, Development）

2. **重新部署**
   - 点击"Deployments"
   - 点击最新部署旁的"..."
   - 选择"Redeploy"

## 📊 验证数据收集

### 实时验证
1. 访问Google Analytics
2. 进入"报告" > "实时"
3. 访问您的网站
4. 应该能在实时报告中看到活跃用户

### 事件验证
在网站上执行以下操作，然后在GA实时报告中查看：
- 搜索项目
- 点击按钮
- 下载文件
- 滚动页面
- 点击外部链接

## 🎨 推荐的GA4设置

### 1. 转化事件设置
在GA4中设置以下转化事件：
- `search`：用户搜索行为
- `file_download`：文件下载
- `user_engagement`：用户参与度

### 2. 自定义维度
创建以下自定义维度：
- 页面类型（首页、报告页、分析页）
- 项目类别（DeFi、NFT、Layer2等）

### 3. 受众群体
创建以下受众群体：
- 活跃用户（多次访问）
- 深度用户（长时间停留）
- 搜索用户（使用搜索功能）

## 🔍 故障排除

### 问题1：数据不显示
**可能原因：**
- GA ID配置错误
- 应用未重启
- 网络问题

**解决方案：**
1. 检查`.env`文件中的GA ID格式
2. 重启应用
3. 检查浏览器控制台错误

### 问题2：事件不触发
**可能原因：**
- JavaScript错误
- 广告拦截器

**解决方案：**
1. 检查浏览器控制台错误
2. 暂时禁用广告拦截器测试
3. 确认gtag函数可用

### 问题3：Vercel环境变量不生效
**解决方案：**
1. 确认环境变量名称正确
2. 确认选择了正确的环境
3. 重新部署应用

## 📱 移动端优化

GA4已自动优化移动端跟踪，但建议：
1. 测试移动设备上的跟踪
2. 检查移动端用户体验
3. 关注移动端性能指标

## 🔒 隐私合规

### GDPR合规建议
1. 添加Cookie同意横幅
2. 提供隐私政策
3. 允许用户选择退出跟踪

### 示例Cookie横幅代码
```html
<div id="cookie-banner" style="display: none;">
    <p>本网站使用Cookie来改善用户体验。</p>
    <button onclick="acceptCookies()">接受</button>
    <button onclick="declineCookies()">拒绝</button>
</div>
```

## 📞 获取帮助

如果遇到问题：
1. 查看 `GOOGLE_ANALYTICS_INTEGRATION.md` 详细文档
2. 运行测试：`python test/test_google_analytics.py`
3. 检查Google Analytics帮助中心
4. 联系开发团队

## ✅ 设置完成检查清单

- [ ] 获取GA4测量ID
- [ ] 配置环境变量
- [ ] 重启应用
- [ ] 验证实时数据
- [ ] 测试自定义事件
- [ ] 设置转化目标
- [ ] 配置自定义维度
- [ ] 添加隐私政策（可选）

完成以上步骤后，您的网站就成功集成了Google Analytics！
