#!/usr/bin/env python3
"""
Google Analytics 状态检查脚本
快速检查GA集成的当前状态
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_ga_status():
    """检查Google Analytics配置状态"""
    print("🔍 Google Analytics 配置状态检查")
    print("=" * 50)
    
    # 检查环境变量
    ga_id = os.getenv('GOOGLE_ANALYTICS_ID')
    
    if ga_id:
        print(f"✅ Google Analytics ID: {ga_id}")
        
        # 验证ID格式
        if ga_id.startswith('G-') and len(ga_id) >= 10:
            print("✅ GA ID 格式正确")
        else:
            print("⚠️  GA ID 格式可能不正确（应为 G-XXXXXXXXXX）")
    else:
        print("❌ 未找到 GOOGLE_ANALYTICS_ID 环境变量")
        print("📋 请在 .env 文件中添加：GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX")
        return False
    
    # 检查应用配置
    try:
        from app import create_app
        app = create_app()
        
        if app.config.get('GOOGLE_ANALYTICS_ID'):
            print("✅ 应用配置中包含 GA ID")
        else:
            print("❌ 应用配置中缺少 GA ID")
            return False
            
    except Exception as e:
        print(f"❌ 应用配置检查失败: {e}")
        return False
    
    # 检查模板文件
    template_path = 'templates/base.html'
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        if 'googletagmanager.com/gtag/js' in template_content:
            print("✅ 模板中包含 GA 跟踪代码")
        else:
            print("❌ 模板中缺少 GA 跟踪代码")
            return False
    else:
        print("❌ 基础模板文件不存在")
        return False
    
    # 检查JavaScript文件
    js_path = 'static/js/main.js'
    if os.path.exists(js_path):
        with open(js_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        if 'trackAnalyticsEvent' in js_content:
            print("✅ JavaScript 中包含 GA 事件跟踪")
        else:
            print("❌ JavaScript 中缺少 GA 事件跟踪")
            return False
    else:
        print("❌ main.js 文件不存在")
        return False
    
    print("\n🎉 Google Analytics 集成状态：完整")
    print("\n📋 下一步操作：")
    print("1. 启动应用：python run.py")
    print("2. 访问网站测试")
    print("3. 在 Google Analytics 实时报告中验证数据")
    
    return True

def show_setup_instructions():
    """显示设置说明"""
    print("\n📚 快速设置指南：")
    print("1. 访问 https://analytics.google.com/")
    print("2. 创建 GA4 属性")
    print("3. 获取测量ID（G-XXXXXXXXXX）")
    print("4. 在 .env 文件中设置：GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX")
    print("5. 重启应用")
    print("\n📖 详细说明请查看：GOOGLE_ANALYTICS_SETUP_GUIDE.md")

if __name__ == '__main__':
    if not check_ga_status():
        show_setup_instructions()
        sys.exit(1)
    else:
        print("\n🚀 准备就绪！您的网站已成功集成 Google Analytics。")
        sys.exit(0)
